export default [
  "strapi::errors",
  {
    name: "strapi::security",
    config: {
      contentSecurityPolicy: {
        directives: {
          "connect-src": ["'self'", "https:"],
          "img-src": [
            "'self'",
            "data:",
            "blob:",
            "res.cloudinary.com",
            "lh3.googleusercontent.com",
            "platform-lookaside.fbsbx.com",
            "dl.airtable.com",
            "s120-ava-talk.zadn.vn",
          ],
          "media-src": [
            "'self'",
            "data:",
            "blob:",
            "res.cloudinary.com",
            "lh3.googleusercontent.com",
            "platform-lookaside.fbsbx.com",
            "dl.airtable.com",
            "s120-ava-talk.zadn.vn",
          ],
        },
      },
    },
  },
  {
    name: "strapi::cors",
    config: {
      origin: ["*"], // Hoặc chỉ định domain cụ thể, ví dụ: ["http://localhost", "https://your-zalo-mini-app.com"]
      methods: ["GET", "POST", "PUT", "PATCH", "DELETE", "OPTIONS"],
      headers: ["Content-Type", "Authorization", "Origin", "Accept"],
    },
  },
  "strapi::poweredBy",
  "strapi::logger",
  "strapi::query",
  "strapi::body",
  "strapi::session",
  "strapi::favicon",
  "strapi::public",
  "global::admin-redirect",
];
